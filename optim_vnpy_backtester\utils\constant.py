"""
Constants used in the backtester
"""

from enum import Enum


class Direction(Enum):
    """
    Direction of order/trade/position.
    """
    LONG = "多"
    SHORT = "空"
    NET = "净"


class Offset(Enum):
    """
    Offset of order/trade.
    """
    OPEN = "开"
    CLOSE = "平"
    CLOSETODAY = "平今"
    CLOSEYESTERDAY = "平昨"


class Status(Enum):
    """
    Order status.
    """
    SUBMITTING = "提交中"
    NOTTRADED = "未成交"
    PARTTRADED = "部分成交"
    ALLTRADED = "全部成交"
    CANCELLED = "已撤销"
    REJECTED = "拒单"


class Exchange(Enum):
    """
    Exchange.
    """
    # Crypto exchanges
    BINANCE = "BINANCE"
    HUOBI = "HUOBI"
    OKEX = "OKEX"
    GATE = "GATE"
    BYBIT = "BYBIT"
    COINBASE = "COINBASE"
    DERIBIT = "DERIBIT"
    BITFINEX = "BITFINEX"
    BITMEX = "BITMEX"
    KRAKEN = "KRAKEN"
    KUCOIN = "KUCOIN"
    BITSTAMP = "BITSTAMP"


class Interval(Enum):
    """
    Time interval of bar data.
    """
    MINUTE = "1m"
    MINUTE_3 = "3m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR = "1h"
    HOUR_2 = "2h"
    HOUR_4 = "4h"
    DAILY = "1d"
    WEEKLY = "1w"


class BacktestingMode(Enum):
    """
    Backtesting mode.
    """
    BAR = "bar"
    TICK = "tick"


class EngineType(Enum):
    """
    Engine type.
    """
    LIVE = "live"
    BACKTESTING = "backtesting"


INTERVAL_DELTA_MAP = {
    Interval.MINUTE: 60,
    Interval.MINUTE_3: 180,
    Interval.MINUTE_5: 300,
    Interval.MINUTE_15: 900,
    Interval.MINUTE_30: 1800,
    Interval.HOUR: 3600,
    Interval.HOUR_2: 7200,
    Interval.HOUR_4: 14400,
    Interval.DAILY: 86400,
    Interval.WEEKLY: 604800,
}
