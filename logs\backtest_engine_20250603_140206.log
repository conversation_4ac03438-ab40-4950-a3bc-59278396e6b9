2025-06-03 14:02:06,916 - backtest_engine - INFO - 设置回测参数: {}
2025-06-03 14:02:06,923 - backtest_engine - INFO - 添加策略: MultiPositionStrategy
2025-06-03 14:02:06,924 - backtest_engine - ERROR - 回测运行出错: Can't instantiate abstract class MultiPositionStrategy without an implementation for abstract method 'on_tick'
2025-06-03 14:02:06,924 - backtest_engine - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\python sc\optim_vnpy_backtester\optim_vnpy_backtester\engines\engine.py", line 197, in run_backtesting
    self.strategy = self.strategy_class(**params)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Can't instantiate abstract class MultiPositionStrategy without an implementation for abstract method 'on_tick'

2025-06-03 14:02:06,925 - backtest_engine - ERROR - 类型错误: Can't instantiate abstract class MultiPositionStrategy without an implementation for abstract method 'on_tick'
2025-06-03 14:02:06,932 - backtest_engine - INFO - 计算结果完成
2025-06-03 14:02:06,933 - backtest_engine - INFO - 计算统计指标完成
