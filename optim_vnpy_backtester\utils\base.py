"""
Base classes and constants for the backtester
"""

from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from typing import Optional, List, Dict, Any

from .constant import Direction, Offset, Status, BacktestingMode, EngineType


STOPORDER_PREFIX = "STOP"


@dataclass
class StopOrder:
    """
    Stop order data for backtesting.
    """
    vt_symbol: str
    direction: Direction
    offset: Offset
    price: float
    volume: float
    stop_orderid: str
    strategy_name: str
    datetime: datetime
    status: Status
    vt_orderids: List[str]


class StopOrderStatus(Enum):
    """
    Stop order status.
    """
    WAITING = "等待中"
    CANCELLED = "已撤销"
    TRIGGERED = "已触发"


class BacktestingResult:
    """
    Backtesting result.
    """

    def __init__(self) -> None:
        """
        Constructor
        """
        self.start_date: datetime = None
        self.end_date: datetime = None
        self.capital: float = 0
        self.end_balance: float = 0
        self.total_return: float = 0
        self.annual_return: float = 0
        self.max_drawdown: float = 0
        self.max_ddpercent: float = 0
        self.total_days: int = 0
        self.profit_days: int = 0
        self.loss_days: int = 0
        self.daily_return: float = 0
        self.return_std: float = 0
        self.sharpe_ratio: float = 0
        self.return_drawdown_ratio: float = 0


@dataclass
class DailyResult:
    """
    Daily result of backtesting.
    """
    date: datetime
    close_price: float
    pre_close: float = 0
    trades: int = 0
    turnover: float = 0
    commission: float = 0
    slippage: float = 0
    trading_pnl: float = 0
    holding_pnl: float = 0
    total_pnl: float = 0
    net_pnl: float = 0

    # 添加新的字段
    start_pos: float = 0
    end_pos: float = 0
    trade_count: int = 0

    def __post_init__(self) -> None:
        """
        Calculate daily result.
        """
        # 检查date是否为datetime类型
        if isinstance(self.date, datetime):
            # 如果是datetime类型，则将时间部分设为0
            self.date = self.date.replace(
                hour=0, minute=0, second=0, microsecond=0)
        # 如果是date类型，则不需要处理
        self.trade_list = []

    def add_trade(self, trade) -> None:
        """
        Add trade to daily result.
        """
        self.trade_list.append(trade)
        self.trade_count += 1

        # 更新成交量和成交额
        self.turnover += trade.price * trade.volume

        # 更新交易次数
        self.trades += 1

    def calculate_pnl(
        self,
        pre_close: float,
        start_pos: float,
        size: float,
        rate: float,
        slippage: float
    ) -> None:
        """
        Calculate profit and loss of daily result - optimized for performance.
        """
        # 记录前一日收盘价和开始持仓
        self.pre_close = pre_close
        self.start_pos = start_pos

        # 计算持仓盈亏 - 使用直接计算，避免中间变量
        price_diff = self.close_price - self.pre_close
        self.holding_pnl = self.start_pos * price_diff * size

        # 计算交易盈亏 - 使用向量化操作
        self.end_pos = self.start_pos

        # 如果没有交易，直接返回
        if not self.trade_list:
            # 计算总盈亏和净盈亏
            self.total_pnl = self.holding_pnl
            self.net_pnl = self.total_pnl
            return

        # 预先计算所有交易的方向和数量
        long_volume = 0.0
        short_volume = 0.0
        long_turnover = 0.0
        short_turnover = 0.0

        # 一次性处理所有交易，减少循环开销
        for trade in self.trade_list:
            # 根据方向更新持仓
            if trade.direction == Direction.LONG:
                pos_change = trade.volume
                self.end_pos += pos_change
                long_volume += trade.volume
                long_turnover += trade.price * trade.volume
            else:
                pos_change = -trade.volume
                self.end_pos += pos_change
                short_volume += trade.volume
                short_turnover += trade.price * trade.volume

            # 计算交易盈亏
            self.trading_pnl += pos_change * \
                (self.close_price - trade.price) * size

        # 计算总成交量和成交额
        total_volume = long_volume + short_volume

        # 计算手续费和滑点 - 一次性计算，避免循环
        self.commission = total_volume * size * rate
        self.slippage = total_volume * size * slippage

        # 计算总盈亏和净盈亏
        self.total_pnl = self.trading_pnl + self.holding_pnl
        self.net_pnl = self.total_pnl - self.commission - self.slippage


@dataclass
class TradeResult:
    """
    Trade result of backtesting.
    """
    vt_symbol: str
    direction: Direction
    offset: Offset
    price: float
    volume: float
    datetime: datetime
    gateway_name: str = ""
    orderid: str = ""
    tradeid: str = ""

    def __post_init__(self) -> None:
        """
        Calculate trade result.
        """
        self.vt_orderid = f"{self.gateway_name}.{self.orderid}"
        self.vt_tradeid = f"{self.gateway_name}.{self.tradeid}"
