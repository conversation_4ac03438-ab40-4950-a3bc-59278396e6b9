2025-06-03 14:00:50,882 - backtest_script - INFO - 加载数据...
2025-06-03 14:00:50,886 - backtest_script - ERROR - 回测脚本运行出错: [Errno 2] No such file or directory: 'data/btcusdt_1h.csv'
2025-06-03 14:00:50,889 - backtest_script - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\python sc\optim_vnpy_backtester\optim_vnpy_backtester\scripts\run_multi_position_strategy.py", line 45, in main
    df = pd.read_csv("data/btcusdt_1h.csv")
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\pandas\io\parsers\readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\pandas\io\parsers\readers.py", line 620, in _read
    parser = TextFileReader(filepath_or_buffer, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\pandas\io\parsers\readers.py", line 1620, in __init__
    self._engine = self._make_engine(f, self.engine)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\pandas\io\parsers\readers.py", line 1880, in _make_engine
    self.handles = get_handle(
                   ^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\pandas\io\common.py", line 873, in get_handle
    handle = open(
             ^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'data/btcusdt_1h.csv'

2025-06-03 14:00:50,890 - backtest_script - ERROR - 文件未找到: [Errno 2] No such file or directory: 'data/btcusdt_1h.csv'
