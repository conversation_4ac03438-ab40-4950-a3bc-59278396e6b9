"""
事件驱动引擎核心组件
"""
from enum import Enum
from queue import Queue, Empty
from threading import Thread
from time import sleep
from typing import Any, Callable, List

class EventType(Enum):
    """
    事件类型枚举
    """
    EVENT_TICK = "eTick."         # Tick行情事件
    EVENT_BAR = "eBar."          # K线行情事件
    EVENT_ORDER = "eOrder."      # 订单事件
    EVENT_TRADE = "eTrade."      # 成交事件
    EVENT_POSITION = "ePosition." # 持仓事件
    EVENT_ACCOUNT = "eAccount."  # 账户事件
    EVENT_LOG = "eLog."          # 日志事件
    EVENT_TIMER = "eTimer."      # 定时器事件
    EVENT_INIT = "eInit."        # 初始化事件
    EVENT_START = "eStart."      # 启动事件
    EVENT_STOP = "eStop."        # 停止事件
    EVENT_SIGNAL = "eSignal."    # 交易信号事件

class Event:
    """
    事件对象
    """
    def __init__(self, type_: EventType, data: Any = None):
        """
        初始化事件
        """
        self.type = type_         # 事件类型
        self.data = data          # 事件数据

class EventEngine:
    """
    事件驱动引擎
    """
    def __init__(self, interval: int = 1):
        """
        初始化事件引擎
        """
        self._interval = interval                 # 事件处理间隔
        self._queue = Queue()                     # 事件队列
        self._active = False                      # 事件引擎开关
        self._thread = Thread(target=self._run)   # 事件处理线程
        self._timer = Thread(target=self._run_timer)  # 计时器线程
        self._handlers = {}                       # 事件处理函数字典
        self._general_handlers = []               # 通用事件处理函数列表

    def _run(self) -> None:
        """
        启动事件处理循环
        """
        while self._active:
            try:
                event = self._queue.get(block=True, timeout=1)
                self._process(event)
            except Empty:
                pass

    def _process(self, event: Event) -> None:
        """
        处理事件
        """
        # 处理特定类型事件
        if event.type in self._handlers:
            [handler(event) for handler in self._handlers[event.type]]

        # 处理通用事件
        if self._general_handlers:
            [handler(event) for handler in self._general_handlers]

    def _run_timer(self) -> None:
        """
        启动计时器，定期推送计时器事件
        """
        while self._active:
            sleep(self._interval)
            event = Event(EventType.EVENT_TIMER)
            self.put(event)

    def start(self) -> None:
        """
        启动引擎
        """
        self._active = True
        self._thread.start()
        self._timer.start()

    def stop(self) -> None:
        """
        停止引擎
        """
        self._active = False
        self._timer.join()
        self._thread.join()

    def put(self, event: Event) -> None:
        """
        将事件放入队列
        """
        self._queue.put(event)

    def register(self, type_: EventType, handler: Callable) -> None:
        """
        注册事件处理函数
        """
        handler_list = self._handlers.setdefault(type_, [])
        if handler not in handler_list:
            handler_list.append(handler)

    def unregister(self, type_: EventType, handler: Callable) -> None:
        """
        注销事件处理函数
        """
        handler_list = self._handlers.get(type_, [])
        if handler in handler_list:
            handler_list.remove(handler)
        if not handler_list:
            self._handlers.pop(type_, None)

    def register_general(self, handler: Callable) -> None:
        """
        注册通用事件处理函数
        """
        if handler not in self._general_handlers:
            self._general_handlers.append(handler)

    def unregister_general(self, handler: Callable) -> None:
        """
        注销通用事件处理函数
        """
        if handler in self._general_handlers:
            self._general_handlers.remove(handler)
