"""
Visualizer for backtesting results
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import pandas as pd
import numpy as np


class BacktestingVisualizer:
    """
    Visualizer for backtesting results.
    """

    def __init__(self) -> None:
        """
        Initialize visualizer.
        """
        self.daily_df: pd.DataFrame = None
        self.trades: List[Dict] = []
        
    def set_daily_df(self, daily_df: pd.DataFrame) -> None:
        """
        Set daily DataFrame.
        """
        self.daily_df = daily_df
        
    def set_trades(self, trades: List[Dict]) -> None:
        """
        Set trade data.
        """
        self.trades = trades
        
    def plot_balance(self) -> None:
        """
        Plot balance curve.
        """
        if self.daily_df is None:
            print("Daily DataFrame not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 6))
            
            plt.title("Balance Curve")
            plt.plot(self.daily_df.index, self.daily_df["balance"])
            
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        except:
            print("Cannot plot balance, matplotlib not installed")
            
    def plot_drawdown(self) -> None:
        """
        Plot drawdown curve.
        """
        if self.daily_df is None:
            print("Daily DataFrame not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 6))
            
            plt.title("Drawdown Curve")
            plt.fill_between(
                self.daily_df.index,
                self.daily_df["drawdown_percent"],
                0,
                alpha=0.3,
                color="r"
            )
            
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        except:
            print("Cannot plot drawdown, matplotlib not installed")
            
    def plot_pnl(self) -> None:
        """
        Plot daily pnl.
        """
        if self.daily_df is None:
            print("Daily DataFrame not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 6))
            
            plt.title("Daily PnL")
            plt.bar(self.daily_df.index, self.daily_df["net_pnl"])
            
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        except:
            print("Cannot plot pnl, matplotlib not installed")
            
    def plot_distribution(self) -> None:
        """
        Plot pnl distribution.
        """
        if self.daily_df is None:
            print("Daily DataFrame not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 6))
            
            plt.title("PnL Distribution")
            sns.histplot(self.daily_df["net_pnl"], kde=True)
            
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        except:
            print("Cannot plot distribution, matplotlib not installed")
            
    def plot_trades(self) -> None:
        """
        Plot trades on price chart.
        """
        if not self.trades:
            print("Trade data not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            sns.set_style("darkgrid")
            plt.figure(figsize=(10, 6))
            
            # Extract price data from trades
            dates = [trade["datetime"] for trade in self.trades]
            prices = [trade["price"] for trade in self.trades]
            
            # Plot price line
            plt.title("Trades")
            plt.plot(dates, prices, color="gray", alpha=0.3)
            
            # Plot buy trades
            buy_dates = [trade["datetime"] for trade in self.trades if trade["direction"] == "多" and trade["offset"] == "开"]
            buy_prices = [trade["price"] for trade in self.trades if trade["direction"] == "多" and trade["offset"] == "开"]
            plt.scatter(buy_dates, buy_prices, color="green", marker="^", s=100, label="Buy")
            
            # Plot sell trades
            sell_dates = [trade["datetime"] for trade in self.trades if trade["direction"] == "空" and trade["offset"] == "开"]
            sell_prices = [trade["price"] for trade in self.trades if trade["direction"] == "空" and trade["offset"] == "开"]
            plt.scatter(sell_dates, sell_prices, color="red", marker="v", s=100, label="Sell")
            
            plt.legend()
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        except:
            print("Cannot plot trades, matplotlib not installed")
            
    def plot_monthly_returns(self) -> None:
        """
        Plot monthly returns.
        """
        if self.daily_df is None:
            print("Daily DataFrame not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # Calculate monthly returns
            self.daily_df["month"] = self.daily_df.index.to_series().apply(lambda x: x.strftime("%Y-%m"))
            monthly_returns = self.daily_df.groupby("month")["net_pnl"].sum()
            
            sns.set_style("darkgrid")
            plt.figure(figsize=(12, 6))
            
            plt.title("Monthly Returns")
            monthly_returns.plot(kind="bar")
            
            plt.grid(True)
            plt.tight_layout()
            plt.show()
        except:
            print("Cannot plot monthly returns, matplotlib not installed")
            
    def create_report(self, filename: str = "backtest_report.html") -> None:
        """
        Create HTML report with all plots.
        """
        if self.daily_df is None:
            print("Daily DataFrame not set")
            return
            
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            from matplotlib.figure import Figure
            from io import BytesIO
            import base64
            
            # Create HTML header
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Backtesting Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .plot { margin-bottom: 30px; }
                    h1, h2 { color: #333; }
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    tr:nth-child(even) { background-color: #f9f9f9; }
                </style>
            </head>
            <body>
                <h1>Backtesting Report</h1>
            """
            
            # Add statistics table
            html += "<h2>Statistics</h2>"
            html += "<table>"
            html += "<tr><th>Metric</th><th>Value</th></tr>"
            
            # Calculate basic statistics
            start_date = self.daily_df.index[0]
            end_date = self.daily_df.index[-1]
            total_days = len(self.daily_df)
            profit_days = len(self.daily_df[self.daily_df["net_pnl"] > 0])
            loss_days = len(self.daily_df[self.daily_df["net_pnl"] < 0])
            
            total_pnl = self.daily_df["net_pnl"].sum()
            
            # Add statistics to table
            html += f"<tr><td>Start Date</td><td>{start_date}</td></tr>"
            html += f"<tr><td>End Date</td><td>{end_date}</td></tr>"
            html += f"<tr><td>Total Days</td><td>{total_days}</td></tr>"
            html += f"<tr><td>Profit Days</td><td>{profit_days}</td></tr>"
            html += f"<tr><td>Loss Days</td><td>{loss_days}</td></tr>"
            html += f"<tr><td>Win Rate</td><td>{profit_days/total_days:.2%}</td></tr>"
            html += f"<tr><td>Total PnL</td><td>{total_pnl:.2f}</td></tr>"
            
            html += "</table>"
            
            # Function to convert plot to base64 image
            def get_plot_image(plot_func):
                plt.figure(figsize=(10, 6))
                plot_func()
                img = BytesIO()
                plt.savefig(img, format='png')
                plt.close()
                img.seek(0)
                return base64.b64encode(img.getvalue()).decode('utf-8')
            
            # Add balance plot
            html += "<h2>Balance Curve</h2>"
            html += "<div class='plot'>"
            html += f"<img src='data:image/png;base64,{get_plot_image(self.plot_balance)}' width='100%'>"
            html += "</div>"
            
            # Add drawdown plot
            html += "<h2>Drawdown</h2>"
            html += "<div class='plot'>"
            html += f"<img src='data:image/png;base64,{get_plot_image(self.plot_drawdown)}' width='100%'>"
            html += "</div>"
            
            # Add daily PnL plot
            html += "<h2>Daily PnL</h2>"
            html += "<div class='plot'>"
            html += f"<img src='data:image/png;base64,{get_plot_image(self.plot_pnl)}' width='100%'>"
            html += "</div>"
            
            # Add PnL distribution plot
            html += "<h2>PnL Distribution</h2>"
            html += "<div class='plot'>"
            html += f"<img src='data:image/png;base64,{get_plot_image(self.plot_distribution)}' width='100%'>"
            html += "</div>"
            
            # Add monthly returns plot
            html += "<h2>Monthly Returns</h2>"
            html += "<div class='plot'>"
            html += f"<img src='data:image/png;base64,{get_plot_image(self.plot_monthly_returns)}' width='100%'>"
            html += "</div>"
            
            # Close HTML
            html += """
            </body>
            </html>
            """
            
            # Write HTML to file
            with open(filename, "w") as f:
                f.write(html)
                
            print(f"Report saved to {filename}")
        except Exception as e:
            print(f"Cannot create report: {e}")
            
    def show_all(self) -> None:
        """
        Show all plots.
        """
        self.plot_balance()
        self.plot_drawdown()
        self.plot_pnl()
        self.plot_distribution()
        self.plot_trades()
        self.plot_monthly_returns()
