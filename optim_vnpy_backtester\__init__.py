"""
VeighNa Backtester - Simple backtesting framework for cryptocurrency trading

目录结构:
- engines: 引擎模块
- templates: 模板模块
- objects: 对象模块
- strategies: 策略模块
- utils: 工具模块
- scripts: 脚本模块
- logs: 日志文件
- charts: 图表文件
- data: 数据文件
"""

from .utils.constant import (
    Direction,
    Offset,
    Exchange,
    Interval,
    Status,
    BacktestingMode,
    EngineType,
    INTERVAL_DELTA_MAP
)

from .objects.object import (
    BarData,
    TickData,
    OrderData,
    TradeData,
    PositionData,
    AccountData
)

from .engines.engine import BacktestingEngine
from .templates.template import StrategyTemplate
from .engines.analyzer import BacktestingAnalyzer
from .engines.visualizer import BacktestingVisualizer
from .engines.data_engine import DataEngine
from .engines.risk_engine import RiskEngine
from .engines.portfolio_engine import PortfolioEngine

# 导入策略模块
from .strategies import (
    MultiPositionStrategy,
    MACrossPositionStrategy
)

# 导入工具模块
from .utils import (
    PlotlyChartEngine,
    LogEngine,
    log_engine,
    TradeLogEngine,
    initialize_trade_log_engine
)

# 导入基础模块
from .utils.base import (
    StopOrder,
    StopOrderStatus,
    BacktestingResult,
    DailyResult,
    TradeResult,
    STOPORDER_PREFIX
)

# 导入工具函数
from .utils.utility import (
    extract_vt_symbol,
    round_to,
    generate_bar_from_ticks,
    load_bar_data_from_csv,
    calculate_statistics
)
